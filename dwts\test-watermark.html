<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水印测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #666;
        }
        .button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #005a87;
        }
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning {
            background: #fff3cd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>DWTS 水印功能测试页面</h1>
        
        <div class="info">
            <strong>说明：</strong>这是一个用于测试DWTS水印功能的页面。如果水印系统正常工作，您应该能看到半透明的水印覆盖在整个页面上。
        </div>

        <div class="test-section">
            <h3>1. 水印显示测试</h3>
            <p>请检查页面是否显示水印。水印应该：</p>
            <ul>
                <li>覆盖整个页面</li>
                <li>半透明显示</li>
                <li>包含IP地址和日期信息</li>
                <li>不影响页面交互</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>2. 控制台调试信息</h3>
            <p>按F12打开开发者工具，查看控制台是否有以下信息：</p>
            <ul>
                <li>[DWTS水印] 开始初始化水印系统</li>
                <li>[DWTS水印] 创建水印元素</li>
                <li>[DWTS水印] 水印已添加到页面</li>
            </ul>
            <button class="button" onclick="checkWatermark()">检查水印状态</button>
        </div>

        <div class="test-section">
            <h3>3. 水印元素检查</h3>
            <p>在开发者工具的Elements面板中，应该能找到：</p>
            <ul>
                <li>ID为 "dwts-watermark-style" 的style标签</li>
                <li>ID为 "dwts-watermark-div" 的div元素</li>
                <li>ID为 "dwts-watermark-script" 的script标签</li>
            </ul>
            <button class="button" onclick="highlightWatermark()">高亮水印元素</button>
        </div>

        <div class="test-section">
            <h3>4. 水印透明度调整</h3>
            <p>如果水印太淡看不清，可以临时调整透明度：</p>
            <button class="button" onclick="adjustOpacity(0.3)">透明度 0.3</button>
            <button class="button" onclick="adjustOpacity(0.5)">透明度 0.5</button>
            <button class="button" onclick="adjustOpacity(0.1)">恢复默认</button>
        </div>

        <div class="warning">
            <strong>注意：</strong>如果看不到水印，可能的原因包括：
            <ul>
                <li>代理配置中的 enable_page_watermark 未启用</li>
                <li>水印透明度设置过低</li>
                <li>浏览器兼容性问题</li>
                <li>页面CSS冲突</li>
            </ul>
        </div>
    </div>

    <script>
        function checkWatermark() {
            const watermark = document.getElementById('dwts-watermark-div');
            const style = document.getElementById('dwts-watermark-style');
            const script = document.getElementById('dwts-watermark-script');
            
            console.log('=== 水印状态检查 ===');
            console.log('水印元素存在:', !!watermark);
            console.log('样式存在:', !!style);
            console.log('脚本存在:', !!script);
            
            if (watermark) {
                console.log('水印元素样式:', watermark.style.cssText);
                console.log('水印元素类名:', watermark.className);
                console.log('水印元素可见性:', getComputedStyle(watermark).visibility);
                console.log('水印元素透明度:', getComputedStyle(watermark).opacity);
            }
            
            alert('检查结果已输出到控制台，请按F12查看');
        }
        
        function highlightWatermark() {
            const watermark = document.getElementById('dwts-watermark-div');
            if (watermark) {
                // 临时添加边框以便查看
                watermark.style.border = '2px solid red';
                setTimeout(() => {
                    watermark.style.border = '';
                }, 3000);
                alert('水印元素已临时添加红色边框，3秒后消失');
            } else {
                alert('未找到水印元素');
            }
        }
        
        function adjustOpacity(opacity) {
            const watermark = document.getElementById('dwts-watermark-div');
            if (watermark) {
                watermark.style.opacity = opacity;
                console.log('水印透明度已调整为:', opacity);
                alert('水印透明度已调整为: ' + opacity);
            } else {
                alert('未找到水印元素');
            }
        }
        
        // 页面加载完成后的检查
        window.addEventListener('load', function() {
            setTimeout(function() {
                console.log('=== 页面加载完成后的水印检查 ===');
                checkWatermark();
            }, 2000);
        });
    </script>
</body>
</html>
