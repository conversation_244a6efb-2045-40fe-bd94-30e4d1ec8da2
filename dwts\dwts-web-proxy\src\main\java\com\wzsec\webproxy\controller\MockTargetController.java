package com.wzsec.webproxy.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 模拟目标服务器控制器
 * 用于测试代理和水印功能
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@RestController
@RequestMapping("/mock")
public class MockTargetController {

    /**
     * 模拟HTML页面
     */
    @GetMapping(value = "/page", produces = MediaType.TEXT_HTML_VALUE)
    public String mockHtmlPage(HttpServletRequest request) {
        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        
        return "<!DOCTYPE html>\n" +
                "<html lang=\"zh-CN\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                "    <title>模拟目标页面</title>\n" +
                "    <style>\n" +
                "        body {\n" +
                "            font-family: 'Microsoft YaHei', Arial, sans-serif;\n" +
                "            margin: 0;\n" +
                "            padding: 20px;\n" +
                "            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);\n" +
                "            min-height: 100vh;\n" +
                "        }\n" +
                "        .container {\n" +
                "            max-width: 800px;\n" +
                "            margin: 0 auto;\n" +
                "            background: white;\n" +
                "            padding: 30px;\n" +
                "            border-radius: 15px;\n" +
                "            box-shadow: 0 10px 30px rgba(0,0,0,0.2);\n" +
                "        }\n" +
                "        h1 {\n" +
                "            color: #2d3436;\n" +
                "            text-align: center;\n" +
                "            margin-bottom: 30px;\n" +
                "        }\n" +
                "        .info-box {\n" +
                "            background: #f8f9fa;\n" +
                "            border-left: 4px solid #74b9ff;\n" +
                "            padding: 15px;\n" +
                "            margin: 15px 0;\n" +
                "            border-radius: 5px;\n" +
                "        }\n" +
                "        .success {\n" +
                "            background: #d4edda;\n" +
                "            border-left-color: #28a745;\n" +
                "        }\n" +
                "        .api-link {\n" +
                "            display: inline-block;\n" +
                "            padding: 10px 20px;\n" +
                "            margin: 10px;\n" +
                "            background: #74b9ff;\n" +
                "            color: white;\n" +
                "            text-decoration: none;\n" +
                "            border-radius: 5px;\n" +
                "            transition: background 0.3s;\n" +
                "        }\n" +
                "        .api-link:hover {\n" +
                "            background: #0984e3;\n" +
                "        }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class=\"container\">\n" +
                "        <h1>🎯 模拟目标页面</h1>\n" +
                "        \n" +
                "        <div class=\"info-box success\">\n" +
                "            <strong>✅ 代理成功！</strong> 如果您看到这个页面，说明代理功能正常工作。\n" +
                "        </div>\n" +
                "        \n" +
                "        <div class=\"info-box\">\n" +
                "            <strong>📊 请求信息：</strong><br>\n" +
                "            <strong>时间：</strong>" + currentTime + "<br>\n" +
                "            <strong>请求路径：</strong>" + request.getRequestURI() + "<br>\n" +
                "            <strong>请求方法：</strong>" + request.getMethod() + "<br>\n" +
                "            <strong>客户端IP：</strong>" + getClientIp(request) + "<br>\n" +
                "            <strong>User-Agent：</strong>" + request.getHeader("User-Agent") + "\n" +
                "        </div>\n" +
                "        \n" +
                "        <div class=\"info-box\">\n" +
                "            <strong>💧 水印测试：</strong><br>\n" +
                "            如果代理配置正确，您应该能看到页面上的水印效果。\n" +
                "        </div>\n" +
                "        \n" +
                "        <div class=\"info-box\">\n" +
                "            <strong>🔗 API测试：</strong><br>\n" +
                "            <a href=\"/mock/api/json\" class=\"api-link\">测试JSON API</a>\n" +
                "            <a href=\"/mock/api/xml\" class=\"api-link\">测试XML API</a>\n" +
                "        </div>\n" +
                "        \n" +
                "        <div class=\"info-box\">\n" +
                "            <strong>🔄 刷新测试：</strong><br>\n" +
                "            <button onclick=\"location.reload()\" class=\"api-link\">刷新页面</button>\n" +
                "            <button onclick=\"testApi()\" class=\"api-link\">测试API</button>\n" +
                "        </div>\n" +
                "    </div>\n" +
                "    \n" +
                "    <script>\n" +
                "        function testApi() {\n" +
                "            fetch('/mock/api/json')\n" +
                "                .then(response => response.json())\n" +
                "                .then(data => {\n" +
                "                    alert('API测试成功：' + JSON.stringify(data, null, 2));\n" +
                "                })\n" +
                "                .catch(error => {\n" +
                "                    alert('API测试失败：' + error.message);\n" +
                "                });\n" +
                "        }\n" +
                "        \n" +
                "        console.log('模拟目标页面加载完成，时间：" + currentTime + "');\n" +
                "    </script>\n" +
                "</body>\n" +
                "</html>";
    }

    /**
     * 模拟JSON API
     */
    @GetMapping(value = "/api/json", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Map<String, Object>> mockJsonApi(HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "这是一个模拟的JSON API响应");
        response.put("timestamp", System.currentTimeMillis());
        response.put("path", request.getRequestURI());
        response.put("method", request.getMethod());
        response.put("clientIp", getClientIp(request));
        
        Map<String, Object> data = new HashMap<>();
        data.put("id", 12345);
        data.put("name", "测试数据");
        data.put("description", "用于测试API水印功能的模拟数据");
        response.put("data", data);
        
        log.info("返回JSON API响应: {}", response);
        
        return ResponseEntity.ok(response);
    }

    /**
     * 模拟XML API
     */
    @GetMapping(value = "/api/xml", produces = MediaType.APPLICATION_XML_VALUE)
    public String mockXmlApi(HttpServletRequest request) {
        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        
        return "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<response>\n" +
                "    <success>true</success>\n" +
                "    <message>这是一个模拟的XML API响应</message>\n" +
                "    <timestamp>" + System.currentTimeMillis() + "</timestamp>\n" +
                "    <path>" + request.getRequestURI() + "</path>\n" +
                "    <method>" + request.getMethod() + "</method>\n" +
                "    <clientIp>" + getClientIp(request) + "</clientIp>\n" +
                "    <data>\n" +
                "        <id>12345</id>\n" +
                "        <name>测试数据</name>\n" +
                "        <description>用于测试API水印功能的模拟数据</description>\n" +
                "    </data>\n" +
                "</response>";
    }

    /**
     * 模拟内网服务API - 统计对比接口
     */
    @GetMapping(value = "/api/home/<USER>/statisticsComparison", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Map<String, Object>> statisticsComparison(HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "统计对比数据获取成功");
        response.put("timestamp", System.currentTimeMillis());

        // 模拟统计数据
        Map<String, Object> data = new HashMap<>();
        data.put("totalUsers", 12580);
        data.put("activeUsers", 8960);
        data.put("newUsers", 1250);
        data.put("growthRate", 15.8);

        Map<String, Object> comparison = new HashMap<>();
        comparison.put("lastMonth", 11200);
        comparison.put("growth", 1380);
        comparison.put("growthPercent", 12.3);

        data.put("comparison", comparison);
        response.put("data", data);

        log.info("返回统计对比API响应: {}", response);

        return ResponseEntity.ok(response);
    }

    /**
     * 模拟内网服务API - 用户列表接口
     */
    @GetMapping(value = "/api/user/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Map<String, Object>> getUserList(HttpServletRequest request,
                                                          @RequestParam(defaultValue = "1") int page,
                                                          @RequestParam(defaultValue = "10") int size) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "用户列表获取成功");

        // 模拟用户数据
        Map<String, Object> data = new HashMap<>();
        data.put("page", page);
        data.put("size", size);
        data.put("total", 156);

        // 模拟用户列表
        java.util.List<Map<String, Object>> users = new java.util.ArrayList<>();
        for (int i = 1; i <= size; i++) {
            Map<String, Object> user = new HashMap<>();
            user.put("id", (page - 1) * size + i);
            user.put("name", "用户" + ((page - 1) * size + i));
            user.put("email", "user" + ((page - 1) * size + i) + "@example.com");
            user.put("status", i % 3 == 0 ? "inactive" : "active");
            users.add(user);
        }

        data.put("list", users);
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    /**
     * 模拟内网服务API - 配置更新接口
     */
    @PostMapping(value = "/api/config/update", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Map<String, Object>> updateConfig(HttpServletRequest request,
                                                           @RequestBody Map<String, Object> configData) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "配置更新成功");
        response.put("timestamp", System.currentTimeMillis());
        response.put("updatedConfig", configData);

        log.info("配置更新请求: {}", configData);

        return ResponseEntity.ok(response);
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }
}
