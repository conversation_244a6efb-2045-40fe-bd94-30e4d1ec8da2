package com.wzsec.webproxy.watermark.impl;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.watermark.AbstractWatermarkProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * HTTP头水印处理器（默认处理器）
 * 用于处理无法直接修改内容的响应类型
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class HeaderWatermarkProcessor extends AbstractWatermarkProcessor {

    @Override
    public byte[] processWatermark(byte[] content, String contentType, 
                                 HttpServletRequest request, WebProxyConfig config) {
        // HTTP头水印不修改响应体内容，只在响应头中添加水印信息
        // 实际的头部添加会在WebProxyService中处理
        return content;
    }

    /**
     * 生成水印头部信息
     * 这个方法会被WebProxyService调用来获取需要添加的头部信息
     * 对包含中文字符的水印文本进行Base64编码，避免HTTP头编码问题
     */
    public String generateWatermarkHeader(HttpServletRequest request, WebProxyConfig config) {
        String watermarkText = generateWatermarkText(request, config);

        // 检查是否包含非ASCII字符
        if (containsNonAscii(watermarkText)) {
            // 对包含中文或其他非ASCII字符的文本进行Base64编码
            String encoded = Base64.getEncoder().encodeToString(watermarkText.getBytes(StandardCharsets.UTF_8));
            log.debug("水印文本包含非ASCII字符，已进行Base64编码: {} -> {}", watermarkText, encoded);
            return "base64:" + encoded;
        }

        return watermarkText;
    }

    /**
     * 检查字符串是否包含非ASCII字符
     */
    private boolean containsNonAscii(String text) {
        if (text == null) {
            return false;
        }

        for (char c : text.toCharArray()) {
            if (c > 127) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean canHandle(String contentType) {
        // 作为默认处理器，可以处理任何类型
        return true;
    }

    @Override
    public String getProcessorName() {
        return "HeaderWatermarkProcessor";
    }

    @Override
    public String getWatermarkType() {
        return "HEADER";
    }
}
